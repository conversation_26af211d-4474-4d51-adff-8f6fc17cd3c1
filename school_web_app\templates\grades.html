{% extends 'base.html' %}

{% block title %}إدارة الدرجات - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">إدارة الدرجات</h5>
        <div>
            <a href="{{ url_for('add_grades') }}" class="btn btn-light">
                <i class="fas fa-plus"></i> إضافة درجات
            </a>
            <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#reportModal">
                <i class="fas fa-file-alt"></i> تقرير الدرجات
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">الفصل</span>
                    <select id="classFilter" class="form-select">
                        <option value="">جميع الفصول</option>
                        {% for class in classes %}
                        <option value="{{ class.id }}">{{ class.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">المادة</span>
                    <select id="subjectFilter" class="form-select">
                        <option value="">جميع المواد</option>
                        {% for subject in subjects %}
                        <option value="{{ subject }}">{{ subject }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" id="searchInput" class="form-control" placeholder="البحث عن طالب...">
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الطالب</th>
                        <th>الفصل</th>
                        <th>المادة</th>
                        <th>الدرجة</th>
                        <th>التاريخ</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for grade in grades %}
                    <tr>
                        <td>{{ grade.id }}</td>
                        <td>{{ grade.student.first_name }} {{ grade.student.last_name }}</td>
                        <td>{{ grade.student.class.name if grade.student.class else 'غير محدد' }}</td>
                        <td>{{ grade.subject }}</td>
                        <td>
                            <span class="badge {% if grade.score >= 90 %}bg-success{% elif grade.score >= 70 %}bg-info{% elif grade.score >= 50 %}bg-warning{% else %}bg-danger{% endif %}">
                                {{ grade.score }}
                            </span>
                        </td>
                        <td>{{ grade.date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <button class="btn btn-sm btn-info edit-grade" data-id="{{ grade.id }}" data-student="{{ grade.student.first_name }} {{ grade.student.last_name }}" data-subject="{{ grade.subject }}" data-score="{{ grade.score }}" data-bs-toggle="modal" data-bs-target="#editGradeModal">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger delete-grade" data-id="{{ grade.id }}" data-bs-toggle="modal" data-bs-target="#deleteGradeModal">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">لا توجد درجات مسجلة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Edit Grade Modal -->
<div class="modal fade" id="editGradeModal" tabindex="-1" aria-labelledby="editGradeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="editGradeModalLabel">تعديل الدرجة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editGradeForm" action="{{ url_for('edit_grade', grade_id=0) }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">الطالب</label>
                        <input type="text" class="form-control" id="studentName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المادة</label>
                        <input type="text" class="form-control" id="subjectName" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="score" class="form-label">الدرجة</label>
                        <input type="number" class="form-control" id="score" name="score" min="0" max="100" step="0.01" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Grade Modal -->
<div class="modal fade" id="deleteGradeModal" tabindex="-1" aria-labelledby="deleteGradeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteGradeModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذه الدرجة؟ هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteGradeForm" action="{{ url_for('delete_grade', grade_id=0) }}" method="POST">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Grades Report Modal -->
<div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-dark text-white">
                <h5 class="modal-title" id="reportModalLabel">تقرير الدرجات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('grades_report') }}" method="GET" target="_blank">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="report_class" class="form-label">الفصل</label>
                        <select class="form-select" id="report_class" name="class_id">
                            <option value="">جميع الفصول</option>
                            {% for class in classes %}
                            <option value="{{ class.id }}">{{ class.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="report_subject" class="form-label">المادة</label>
                        <select class="form-select" id="report_subject" name="subject">
                            <option value="">جميع المواد</option>
                            {% for subject in subjects %}
                            <option value="{{ subject }}">{{ subject }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="report_student" class="form-label">الطالب (اختياري)</label>
                        <select class="form-select" id="report_student" name="student_id">
                            <option value="">جميع الطلاب</option>
                            {% for student in students %}
                            <option value="{{ student.id }}">{{ student.first_name }} {{ student.last_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-dark">عرض التقرير</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Search functionality
    document.getElementById('searchInput').addEventListener('keyup', function() {
        filterGrades();
    });
    
    // Class filter
    document.getElementById('classFilter').addEventListener('change', function() {
        filterGrades();
    });
    
    // Subject filter
    document.getElementById('subjectFilter').addEventListener('change', function() {
        filterGrades();
    });
    
    // Filter grades based on search, class and subject
    function filterGrades() {
        const searchValue = document.getElementById('searchInput').value.toLowerCase();
        const classId = document.getElementById('classFilter').value;
        const subject = document.getElementById('subjectFilter').value;
        const tableRows = document.querySelectorAll('tbody tr');
        
        tableRows.forEach(row => {
            let display = true;
            const text = row.textContent.toLowerCase();
            
            if (searchValue && !text.includes(searchValue)) {
                display = false;
            }
            
            if (classId && !row.cells[2].textContent.includes(classId)) {
                display = false;
            }
            
            if (subject && !row.cells[3].textContent.includes(subject)) {
                display = false;
            }
            
            row.style.display = display ? '' : 'none';
        });
    }
    
    // Edit grade
    document.querySelectorAll('.edit-grade').forEach(button => {
        button.addEventListener('click', function() {
            const gradeId = this.getAttribute('data-id');
            const studentName = this.getAttribute('data-student');
            const subject = this.getAttribute('data-subject');
            const score = this.getAttribute('data-score');
            
            document.getElementById('studentName').value = studentName;
            document.getElementById('subjectName').value = subject;
            document.getElementById('score').value = score;
            
            // Update form action
            const form = document.getElementById('editGradeForm');
            form.action = form.action.replace('/0', `/${gradeId}`);
        });
    });
    
    // Delete grade
    document.querySelectorAll('.delete-grade').forEach(button => {
        button.addEventListener('click', function() {
            const gradeId = this.getAttribute('data-id');
            const form = document.getElementById('deleteGradeForm');
            form.action = form.action.replace('/0', `/${gradeId}`);
        });
    });
</script>
{% endblock %}
