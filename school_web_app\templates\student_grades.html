{% extends 'base.html' %}

{% block title %}درجات {{ student.first_name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">درجات: {{ student.first_name }} {{ student.last_name }}</h2>
    
    {% if subjects %}
        {% for subject, grades in subjects.items() %}
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">{{ subject }}</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الدرجة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for grade in grades %}
                        <tr>
                            <td>{{ grade.date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ grade.score }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="alert alert-info">
            لا توجد درجات مسجلة لهذا الطالب.
        </div>
    {% endif %}
    
    <a href="{{ url_for('index') }}" class="btn btn-secondary">رجوع</a>
</div>
{% endblock %}