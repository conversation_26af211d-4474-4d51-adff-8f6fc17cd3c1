{% extends 'base.html' %}

{% block title %}تسجيل درجات {{ class_obj.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">تسجيل درجات: {{ class_obj.name }}</h2>
    
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">تسجيل الدرجات</h5>
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="subject">المادة:</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="date">التاريخ:</label>
                            <input type="date" class="form-control" id="date" name="date" value="{{ today }}" required>
                        </div>
                    </div>
                </div>
                
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم الطالب</th>
                            <th>الدرجة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for student in students %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ student.first_name }} {{ student.last_name }}</td>
                            <td>
                                <input type="number" class="form-control" name="score_{{ student.id }}" min="0" max="100" step="0.01">
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                
                <button type="submit" class="btn btn-primary">حفظ</button>
                <a href="{{ url_for('grades') }}" class="btn btn-secondary">رجوع</a>
            </form>
        </div>
    </div>
</div>
{% endblock %}