{% extends 'base.html' %}

{% block title %}اختيار الفصل لتسجيل الحضور - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-warning text-dark">
        <h5 class="mb-0">اختيار الفصل لتسجيل الحضور</h5>
    </div>
    <div class="card-body">
        <form action="{{ url_for('take_attendance') }}" method="GET">
            <div class="mb-3">
                <label for="class_id" class="form-label">الفصل</label>
                <select class="form-select" id="class_id" name="class_id" required>
                    <option value="">اختر الفصل...</option>
                    {% for class in classes %}
                    <option value="{{ class.id }}">{{ class.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="mb-3">
                <label for="date" class="form-label">التاريخ</label>
                <input type="date" class="form-control" id="date" name="date" value="{{ date }}" required>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('attendance') }}" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">متابعة</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
