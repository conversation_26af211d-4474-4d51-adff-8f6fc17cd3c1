{% extends 'base.html' %}

{% block title %}إضافة الدرجات - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">إضافة الدرجات - {{ class_obj.name }} - {{ subject }}</h5>
        <a href="{{ url_for('grades') }}" class="btn btn-light">
            <i class="fas fa-arrow-right"></i> العودة إلى الدرجات
        </a>
    </div>
    <div class="card-body">
        <form action="{{ url_for('add_grades') }}" method="POST">
            <input type="hidden" name="class_id" value="{{ class_obj.id }}">
            <input type="hidden" name="subject" value="{{ subject }}">
            
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">التاريخ</span>
                        <input type="date" name="date" class="form-control" value="{{ date }}" required>
                    </div>
                </div>
                <div class="col-md-8 text-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-secondary" id="clearAllGrades">مسح جميع الدرجات</button>
                    </div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الطالب</th>
                            <th>الدرجة (0-100)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for student in students %}
                        <tr>
                            <td>{{ student.id }}</td>
                            <td>{{ student.first_name }} {{ student.last_name }}</td>
                            <td>
                                <input type="hidden" name="student_id_{{ loop.index }}" value="{{ student.id }}">
                                <input type="number" class="form-control" name="grade_{{ loop.index }}" min="0" max="100" step="0.01" value="{{ grades.get(student.id, '') }}">
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="3" class="text-center">لا يوجد طلاب في هذا الفصل</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-between mt-3">
                <a href="{{ url_for('grades') }}" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ الدرجات</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Clear all grades
    document.getElementById('clearAllGrades').addEventListener('click', function() {
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.value = '';
        });
    });
</script>
{% endblock %}
