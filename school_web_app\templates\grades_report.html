{% extends 'base.html' %}

{% block title %}تقرير الدرجات - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقرير الدرجات</h5>
        <button class="btn btn-light" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة التقرير
        </button>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr class="table-dark">
                        <th>#</th>
                        <th>الطالب</th>
                        <th>الفصل</th>
                        <th>المادة</th>
                        <th>الدرجة</th>
                        <th>التقدير</th>
                        <th>التاريخ</th>
                    </tr>
                </thead>
                <tbody>
                    {% for grade in grades %}
                    <tr>
                        <td>{{ grade.id }}</td>
                        <td>{{ grade.student.first_name }} {{ grade.student.last_name }}</td>
                        <td>{{ grade.student.class.name if grade.student.class else 'غير محدد' }}</td>
                        <td>{{ grade.subject }}</td>
                        <td>{{ grade.score }}</td>
                        <td>
                            {% if grade.score >= 90 %}
                                <span class="badge bg-success">ممتاز</span>
                            {% elif grade.score >= 80 %}
                                <span class="badge bg-info">جيد جداً</span>
                            {% elif grade.score >= 70 %}
                                <span class="badge bg-primary">جيد</span>
                            {% elif grade.score >= 60 %}
                                <span class="badge bg-warning">مقبول</span>
                            {% else %}
                                <span class="badge bg-danger">ضعيف</span>
                            {% endif %}
                        </td>
                        <td>{{ grade.date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">لا توجد درجات مسجلة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        {% if grades %}
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">ملخص الدرجات</h6>
                    </div>
                    <div class="card-body">
                        {% set total_score = 0 %}
                        {% for grade in grades %}
                            {% set total_score = total_score + grade.score %}
                        {% endfor %}
                        {% set avg_score = (total_score / grades|length) if grades|length > 0 else 0 %}
                        
                        {% set excellent = grades|selectattr('score', 'ge', 90)|list|length %}
                        {% set very_good = grades|selectattr('score', 'ge', 80)|selectattr('score', 'lt', 90)|list|length %}
                        {% set good = grades|selectattr('score', 'ge', 70)|selectattr('score', 'lt', 80)|list|length %}
                        {% set pass = grades|selectattr('score', 'ge', 60)|selectattr('score', 'lt', 70)|list|length %}
                        {% set fail = grades|selectattr('score', 'lt', 60)|list|length %}
                        
                        <div class="row">
                            <div class="col-6">
                                <p><strong>إجمالي الدرجات:</strong></p>
                                <p><strong>متوسط الدرجات:</strong></p>
                                <p><strong>أعلى درجة:</strong></p>
                                <p><strong>أدنى درجة:</strong></p>
                            </div>
                            <div class="col-6 text-end">
                                <p>{{ grades|length }}</p>
                                <p>{{ avg_score|round(2) }}</p>
                                <p>{{ grades|map(attribute='score')|max if grades else 0 }}</p>
                                <p>{{ grades|map(attribute='score')|min if grades else 0 }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">توزيع التقديرات</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <p><strong>ممتاز (90-100):</strong></p>
                                <p><strong>جيد جداً (80-89):</strong></p>
                                <p><strong>جيد (70-79):</strong></p>
                                <p><strong>مقبول (60-69):</strong></p>
                                <p><strong>ضعيف (0-59):</strong></p>
                            </div>
                            <div class="col-6 text-end">
                                <p>{{ excellent }} ({{ (excellent / grades|length * 100)|round(2) if grades|length > 0 else 0 }}%)</p>
                                <p>{{ very_good }} ({{ (very_good / grades|length * 100)|round(2) if grades|length > 0 else 0 }}%)</p>
                                <p>{{ good }} ({{ (good / grades|length * 100)|round(2) if grades|length > 0 else 0 }}%)</p>
                                <p>{{ pass }} ({{ (pass / grades|length * 100)|round(2) if grades|length > 0 else 0 }}%)</p>
                                <p>{{ fail }} ({{ (fail / grades|length * 100)|round(2) if grades|length > 0 else 0 }}%)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        .navbar, .footer, .btn, .no-print {
            display: none !important;
        }
        
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        
        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
            border-bottom: 1px solid #dee2e6 !important;
        }
        
        .badge {
            border: 1px solid #000 !important;
            color: #000 !important;
        }
        
        .bg-success {
            background-color: #ffffff !important;
            border: 1px solid #28a745 !important;
        }
        
        .bg-info {
            background-color: #ffffff !important;
            border: 1px solid #17a2b8 !important;
        }
        
        .bg-primary {
            background-color: #ffffff !important;
            border: 1px solid #007bff !important;
        }
        
        .bg-warning {
            background-color: #ffffff !important;
            border: 1px solid #ffc107 !important;
        }
        
        .bg-danger {
            background-color: #ffffff !important;
            border: 1px solid #dc3545 !important;
        }
        
        .table-dark th {
            background-color: #f8f9fa !important;
            color: #000 !important;
            border: 1px solid #dee2e6 !important;
        }
    }
</style>
{% endblock %}
