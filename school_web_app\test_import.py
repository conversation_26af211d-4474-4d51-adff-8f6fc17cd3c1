#!/usr/bin/env python3
"""Test script to check if routes can be imported without issues"""

print("Starting import test...")

try:
    print("1. Testing basic imports...")
    from flask import Flask
    print("   Flask imported successfully")
    
    from flask_sqlalchemy import SQLAlchemy
    print("   SQLAlchemy imported successfully")
    
    print("2. Testing routes import...")
    from routes import register_routes
    print("   Routes imported successfully")
    
    print("3. Creating minimal Flask app...")
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db = SQLAlchemy(app)
    print("   Flask app created successfully")
    
    print("4. All imports successful!")
    
except Exception as e:
    print(f"Error during import: {e}")
    import traceback
    traceback.print_exc()
