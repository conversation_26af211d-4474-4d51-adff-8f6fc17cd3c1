{% extends 'base.html' %}

{% block title %}إدارة الفصول - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">إدارة الفصول</h5>
        <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addClassModal">
            <i class="fas fa-plus"></i> إضافة فصل جديد
        </button>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" id="searchInput" class="form-control" placeholder="البحث عن فصل...">
                </div>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">المستوى</span>
                    <select id="gradeFilter" class="form-select">
                        <option value="">جميع المستويات</option>
                        {% for i in range(1, 13) %}
                        <option value="{{ i }}">{{ i }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الفصل</th>
                        <th>المستوى</th>
                        <th>المعلم المسؤول</th>
                        <th>عدد الطلاب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for class in classes %}
                    <tr>
                        <td>{{ class.id }}</td>
                        <td>{{ class.name }}</td>
                        <td>{{ class.grade_level }}</td>
                        <td>
                            {% if class.teacher_id %}
                                {% for teacher in teachers %}
                                    {% if teacher.id == class.teacher_id %}
                                        {{ teacher.first_name }} {{ teacher.last_name }}
                                    {% endif %}
                                {% endfor %}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </td>
                        <td>{{ class.students|length }}</td>
                        <td>
                            <a href="{{ url_for('edit_class', class_id=class.id) }}" class="btn btn-sm btn-info">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{{ url_for('class_students', class_id=class.id) }}" class="btn btn-sm btn-primary">
                                <i class="fas fa-users"></i>
                            </a>
                            <button class="btn btn-sm btn-danger delete-class" data-id="{{ class.id }}" data-bs-toggle="modal" data-bs-target="#deleteClassModal">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center">لا توجد فصول مسجلة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Class Modal -->
<div class="modal fade" id="addClassModal" tabindex="-1" aria-labelledby="addClassModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="addClassModalLabel">إضافة فصل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('add_class') }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم الفصل</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="grade_level" class="form-label">المستوى</label>
                        <select class="form-select" id="grade_level" name="grade_level" required>
                            <option value="">اختر المستوى...</option>
                            {% for i in range(1, 13) %}
                            <option value="{{ i }}">{{ i }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="teacher_id" class="form-label">المعلم المسؤول</label>
                        <select class="form-select" id="teacher_id" name="teacher_id">
                            <option value="">اختر المعلم...</option>
                            {% for teacher in teachers %}
                            <option value="{{ teacher.id }}">{{ teacher.first_name }} {{ teacher.last_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteClassModal" tabindex="-1" aria-labelledby="deleteClassModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteClassModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذا الفصل؟ هذا الإجراء لا يمكن التراجع عنه.</p>
                <p class="text-danger">ملاحظة: سيتم إزالة ارتباط جميع الطلاب بهذا الفصل.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteClassForm" action="{{ url_for('delete_class', class_id=0) }}" method="POST">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Search functionality
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchValue = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchValue) ? '' : 'none';
        });
    });
    
    // Grade level filter
    document.getElementById('gradeFilter').addEventListener('change', function() {
        const gradeLevel = this.value;
        const tableRows = document.querySelectorAll('tbody tr');
        
        tableRows.forEach(row => {
            if (!gradeLevel) {
                row.style.display = '';
                return;
            }
            
            const gradeLevelCell = row.cells[2].textContent;
            const matchesGradeLevel = gradeLevelCell === gradeLevel;
            row.style.display = matchesGradeLevel ? '' : 'none';
        });
    });
    
    // Delete class
    document.querySelectorAll('.delete-class').forEach(button => {
        button.addEventListener('click', function() {
            const classId = this.getAttribute('data-id');
            const form = document.getElementById('deleteClassForm');
            form.action = form.action.replace('/0', `/${classId}`);
        });
    });
</script>
{% endblock %}
