from app import app, db, login_required, admin_required, teacher_or_admin_required
from flask import render_template, request, redirect, url_for, flash, session, jsonify
from app import User, Student, Teacher, Class, Attendance, Grade
from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash

# Student routes
@app.route('/students')
@app.route('/students/<int:page>')
@teacher_or_admin_required
def students(page=1):
    students = Student.query.paginate(page=page, per_page=10)
    classes = Class.query.all()
    return render_template('students.html', students=students, classes=classes)

@app.route('/add_student', methods=['POST'])
@teacher_or_admin_required
def add_student():
    first_name = request.form['first_name']
    last_name = request.form['last_name']
    date_of_birth = datetime.strptime(request.form['date_of_birth'], '%Y-%m-%d').date()
    gender = request.form['gender']
    address = request.form.get('address', '')
    phone = request.form.get('phone', '')
    email = request.form.get('email', '')
    class_id = request.form.get('class_id', None)

    # Create a new student
    student = Student(
        first_name=first_name,
        last_name=last_name,
        date_of_birth=date_of_birth,
        gender=gender,
        address=address,
        phone=phone,
        email=email,
        enrollment_date=date.today()
    )

    if class_id:
        student.class_id = class_id

    db.session.add(student)
    db.session.commit()

    flash('تمت إضافة الطالب بنجاح', 'success')
    return redirect(url_for('students'))

@app.route('/edit_student/<int:student_id>', methods=['GET', 'POST'])
@teacher_or_admin_required
def edit_student(student_id):
    student = Student.query.get_or_404(student_id)
    classes = Class.query.all()

    if request.method == 'POST':
        student.first_name = request.form['first_name']
        student.last_name = request.form['last_name']
        student.date_of_birth = datetime.strptime(request.form['date_of_birth'], '%Y-%m-%d').date()
        student.gender = request.form['gender']
        student.address = request.form.get('address', '')
        student.phone = request.form.get('phone', '')
        student.email = request.form.get('email', '')
        student.class_id = request.form.get('class_id', None)

        db.session.commit()
        flash('تم تحديث بيانات الطالب بنجاح', 'success')
        return redirect(url_for('students'))

    return render_template('edit_student.html', student=student, classes=classes)

@app.route('/delete_student/<int:student_id>', methods=['POST'])
@admin_required
def delete_student(student_id):
    student = Student.query.get_or_404(student_id)

    # Delete related records
    Attendance.query.filter_by(student_id=student_id).delete()
    Grade.query.filter_by(student_id=student_id).delete()

    db.session.delete(student)
    db.session.commit()

    flash('تم حذف الطالب بنجاح', 'success')
    return redirect(url_for('students'))

# Teacher routes
@app.route('/teachers')
@app.route('/teachers/<int:page>')
@admin_required
def teachers(page=1):
    teachers = Teacher.query.paginate(page=page, per_page=10)
    subjects = db.session.query(Teacher.subject).distinct().all()
    return render_template('teachers.html', teachers=teachers, subjects=[s[0] for s in subjects if s[0]])

@app.route('/add_teacher', methods=['POST'])
@admin_required
def add_teacher():
    first_name = request.form['first_name']
    last_name = request.form['last_name']
    date_of_birth = datetime.strptime(request.form['date_of_birth'], '%Y-%m-%d').date()
    gender = request.form['gender']
    address = request.form.get('address', '')
    phone = request.form.get('phone', '')
    email = request.form.get('email', '')
    hire_date = datetime.strptime(request.form['hire_date'], '%Y-%m-%d').date()
    subject = request.form['subject']

    # Create a new teacher
    teacher = Teacher(
        first_name=first_name,
        last_name=last_name,
        date_of_birth=date_of_birth,
        gender=gender,
        address=address,
        phone=phone,
        email=email,
        hire_date=hire_date,
        subject=subject
    )

    db.session.add(teacher)
    db.session.commit()

    flash('تمت إضافة المعلم بنجاح', 'success')
    return redirect(url_for('teachers'))

@app.route('/edit_teacher/<int:teacher_id>', methods=['GET', 'POST'])
@admin_required
def edit_teacher(teacher_id):
    teacher = Teacher.query.get_or_404(teacher_id)

    if request.method == 'POST':
        teacher.first_name = request.form['first_name']
        teacher.last_name = request.form['last_name']
        teacher.date_of_birth = datetime.strptime(request.form['date_of_birth'], '%Y-%m-%d').date()
        teacher.gender = request.form['gender']
        teacher.address = request.form.get('address', '')
        teacher.phone = request.form.get('phone', '')
        teacher.email = request.form.get('email', '')
        teacher.hire_date = datetime.strptime(request.form['hire_date'], '%Y-%m-%d').date()
        teacher.subject = request.form['subject']

        db.session.commit()
        flash('تم تحديث بيانات المعلم بنجاح', 'success')
        return redirect(url_for('teachers'))

    return render_template('edit_teacher.html', teacher=teacher)

@app.route('/delete_teacher/<int:teacher_id>', methods=['POST'])
@admin_required
def delete_teacher(teacher_id):
    teacher = Teacher.query.get_or_404(teacher_id)

    # Update classes to remove this teacher
    classes = Class.query.filter_by(teacher_id=teacher_id).all()
    for cls in classes:
        cls.teacher_id = None

    db.session.delete(teacher)
    db.session.commit()

    flash('تم حذف المعلم بنجاح', 'success')
    return redirect(url_for('teachers'))

# Class routes
@app.route('/classes')
@admin_required
def classes():
    classes = Class.query.all()
    teachers = Teacher.query.all()
    return render_template('classes.html', classes=classes, teachers=teachers)

@app.route('/add_class', methods=['POST'])
@admin_required
def add_class():
    name = request.form['name']
    grade_level = request.form['grade_level']
    teacher_id = request.form.get('teacher_id', None)

    # Create a new class
    new_class = Class(
        name=name,
        grade_level=grade_level
    )

    if teacher_id:
        new_class.teacher_id = teacher_id

    db.session.add(new_class)
    db.session.commit()

    flash('تمت إضافة الفصل بنجاح', 'success')
    return redirect(url_for('classes'))

@app.route('/edit_class/<int:class_id>', methods=['GET', 'POST'])
@admin_required
def edit_class(class_id):
    class_obj = Class.query.get_or_404(class_id)
    teachers = Teacher.query.all()

    if request.method == 'POST':
        class_obj.name = request.form['name']
        class_obj.grade_level = request.form['grade_level']
        class_obj.teacher_id = request.form.get('teacher_id', None)

        db.session.commit()
        flash('تم تحديث بيانات الفصل بنجاح', 'success')
        return redirect(url_for('classes'))

    return render_template('edit_class.html', class_obj=class_obj, teachers=teachers)

@app.route('/delete_class/<int:class_id>', methods=['POST'])
@admin_required
def delete_class(class_id):
    class_obj = Class.query.get_or_404(class_id)

    # Update students to remove this class
    students = Student.query.filter_by(class_id=class_id).all()
    for student in students:
        student.class_id = None

    db.session.delete(class_obj)
    db.session.commit()

    flash('تم حذف الفصل بنجاح', 'success')
    return redirect(url_for('classes'))

@app.route('/class_students/<int:class_id>')
@teacher_or_admin_required
def class_students(class_id):
    class_obj = Class.query.get_or_404(class_id)
    students = Student.query.filter_by(class_id=class_id).all()
    return render_template('class_students.html', class_obj=class_obj, students=students)

# Attendance routes
@app.route('/attendance')
@login_required
def attendance():
    today = date.today()
    classes = Class.query.all()
    students = Student.query.all()

    # Get today's attendance records
    attendances = Attendance.query.filter_by(date=today).all()

    return render_template('attendance.html',
                          attendances=attendances,
                          classes=classes,
                          students=students,
                          today=today.strftime('%Y-%m-%d'))

@app.route('/take_attendance', methods=['GET', 'POST'])
@teacher_or_admin_required
def take_attendance():
    if request.method == 'POST':
        class_id = request.form['class_id']
        attendance_date = datetime.strptime(request.form['date'], '%Y-%m-%d').date()

        # Get all students in the class
        students = Student.query.filter_by(class_id=class_id).all()

        # Delete existing attendance records for this class and date
        for student in students:
            existing = Attendance.query.filter_by(student_id=student.id, date=attendance_date).first()
            if existing:
                db.session.delete(existing)

        # Add new attendance records
        for key, value in request.form.items():
            if key.startswith('status_'):
                student_id = int(key.split('_')[1])
                status = value

                attendance = Attendance(
                    student_id=student_id,
                    date=attendance_date,
                    status=status
                )

                db.session.add(attendance)

        db.session.commit()

        flash('تم تسجيل الحضور بنجاح', 'success')
        return redirect(url_for('attendance'))

    # GET request - show form
    class_id = request.args.get('class_id', '')
    date_str = request.args.get('date', date.today().strftime('%Y-%m-%d'))

    if class_id:
        class_obj = Class.query.get_or_404(class_id)
        students = Student.query.filter_by(class_id=class_id).all()

        # Get existing attendance records
        attendance_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        attendances = {}
        for student in students:
            record = Attendance.query.filter_by(student_id=student.id, date=attendance_date).first()
            if record:
                attendances[student.id] = record.status

        return render_template('take_attendance.html',
                              class_obj=class_obj,
                              students=students,
                              date=date_str,
                              attendances=attendances)

    # If no class selected, show class selection form
    classes = Class.query.all()
    return render_template('select_class_for_attendance.html',
                          classes=classes,
                          date=date_str)

@app.route('/attendance_report')
@login_required
def attendance_report():
    # Get filter parameters
    class_id = request.args.get('class_id', '')
    student_id = request.args.get('student_id', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')

    # Get all classes and students for filter dropdowns
    classes = Class.query.all()
    students = Student.query.all()

    # Build query based on filters
    query = Attendance.query

    if student_id:
        query = query.filter_by(student_id=student_id)
    elif class_id:
        class_students = Student.query.filter_by(class_id=class_id).all()
        student_ids = [s.id for s in class_students]
        if student_ids:  # Only apply filter if there are students in the class
            query = query.filter(Attendance.student_id.in_(student_ids))

    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(Attendance.date >= start_date_obj)
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'danger')

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(Attendance.date <= end_date_obj)
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'danger')

    # Get filtered attendance records
    attendances = query.order_by(Attendance.date.desc()).all()

    return render_template('attendance_report.html', 
                         attendances=attendances,
                         classes=classes,
                         students=students)

# Grade routes
@app.route('/grades')
@login_required
def grades():
    classes = Class.query.all()
    students = Student.query.all()

    # Get all grades
    all_grades = Grade.query.all()

    # Get unique subjects
    subjects = db.session.query(Grade.subject).distinct().all()

    return render_template('grades.html',
                          grades=all_grades,
                          classes=classes,
                          students=students,
                          subjects=[s[0] for s in subjects])

@app.route('/add_grades', methods=['GET', 'POST'])
@teacher_or_admin_required
def add_grades():
    if request.method == 'POST':
        class_id = request.form['class_id']
        subject = request.form['subject']
        grade_date = datetime.strptime(request.form['date'], '%Y-%m-%d').date()

        # Add grades for each student
        for key, value in request.form.items():
            if key.startswith('grade_'):
                student_id = request.form.get(f'student_id_{key.split("_")[1]}')
                if student_id and value.strip():
                    try:
                        score = float(value)

                        # Check if grade already exists
                        existing = Grade.query.filter_by(student_id=student_id, subject=subject).first()

                        if existing:
                            existing.score = score
                            existing.date = grade_date
                        else:
                            grade = Grade(
                                student_id=student_id,
                                subject=subject,
                                score=score,
                                date=grade_date
                            )
                            db.session.add(grade)
                    except ValueError:
                        flash(f'قيمة غير صالحة للدرجة: {value}', 'danger')
                        return redirect(url_for('add_grades'))

        db.session.commit()

        flash('تم تسجيل الدرجات بنجاح', 'success')
        return redirect(url_for('grades'))

    # GET request - show form
    class_id = request.args.get('class_id', '')
    subject = request.args.get('subject', '')

    if class_id and subject:
        class_obj = Class.query.get_or_404(class_id)
        students = Student.query.filter_by(class_id=class_id).all()

        # Get existing grades
        grades = {}
        for student in students:
            grade = Grade.query.filter_by(student_id=student.id, subject=subject).first()
            if grade:
                grades[student.id] = grade.score

        return render_template('add_grades.html',
                              class_obj=class_obj,
                              students=students,
                              subject=subject,
                              grades=grades,
                              date=date.today().strftime('%Y-%m-%d'))

    # If no class or subject selected, show selection form
    classes = Class.query.all()
    return render_template('select_class_for_grades.html', classes=classes)

@app.route('/delete_grade/<int:grade_id>', methods=['POST'])
@teacher_or_admin_required
def delete_grade(grade_id):
    grade = Grade.query.get_or_404(grade_id)

    db.session.delete(grade)
    db.session.commit()

    flash('تم حذف الدرجة بنجاح', 'success')
    return redirect(url_for('grades'))

@app.route('/grades_report')
@login_required
def grades_report():
    class_id = request.args.get('class_id', '')
    subject = request.args.get('subject', '')
    student_id = request.args.get('student_id', '')

    # Build query based on filters
    query = Grade.query

    if student_id:
        query = query.filter_by(student_id=student_id)
    elif class_id:
        students = Student.query.filter_by(class_id=class_id).all()
        student_ids = [s.id for s in students]
        query = query.filter(Grade.student_id.in_(student_ids))

    if subject:
        query = query.filter_by(subject=subject)

    grades = query.order_by(Grade.date.desc()).all()

    # For a real application, you would generate a proper report
    # For this demo, we'll just return the grade records
    return render_template('grades_report.html', grades=grades)

# Profile and settings
@app.route('/profile')
@login_required
def profile():
    user = User.query.get(session['user_id'])

    # Get user-specific data
    teacher = None
    student = None
    stats = {}

    if user.role == 'admin':
        stats = {
            'student_count': Student.query.count(),
            'teacher_count': Teacher.query.count(),
            'class_count': Class.query.count()
        }
    elif user.role == 'teacher':
        teacher = Teacher.query.filter_by(user_id=user.id).first()
        if teacher:
            classes = Class.query.filter_by(teacher_id=teacher.id).all()
            student_count = sum(len(c.students) for c in classes)
            avg_grade = 0
            grades = Grade.query.filter_by(subject=teacher.subject).all()
            if grades:
                avg_grade = sum(g.score for g in grades) / len(grades)

            stats = {
                'class_count': len(classes),
                'student_count': student_count,
                'avg_grade': round(avg_grade, 2)
            }
    elif user.role == 'student':
        student = Student.query.filter_by(user_id=user.id).first()
        if student:
            grades = Grade.query.filter_by(student_id=student.id).all()
            subjects = set(g.subject for g in grades)
            avg_grade = 0
            if grades:
                avg_grade = sum(g.score for g in grades) / len(grades)

            # Calculate attendance rate
            total_days = Attendance.query.filter_by(student_id=student.id).count()
            present_days = Attendance.query.filter_by(student_id=student.id, status='حاضر').count()
            attendance_rate = int((present_days / total_days) * 100) if total_days > 0 else 0

            stats = {
                'subject_count': len(subjects),
                'avg_grade': round(avg_grade, 2),
                'attendance_rate': attendance_rate
            }

    return render_template('profile.html',
                          user=user,
                          teacher=teacher,
                          student=student,
                          stats=stats)

@app.route('/change_password', methods=['POST'])
@login_required
def change_password():
    current_password = request.form['current_password']
    new_password = request.form['new_password']
    confirm_password = request.form['confirm_password']

    user = User.query.get(session['user_id'])

    if not check_password_hash(user.password, current_password):
        flash('كلمة المرور الحالية غير صحيحة', 'danger')
        return redirect(url_for('profile'))

    if new_password != confirm_password:
        flash('كلمة المرور الجديدة وتأكيدها غير متطابقين', 'danger')
        return redirect(url_for('profile'))

    user.password = generate_password_hash(new_password)
    db.session.commit()

    flash('تم تغيير كلمة المرور بنجاح', 'success')
    return redirect(url_for('profile'))


