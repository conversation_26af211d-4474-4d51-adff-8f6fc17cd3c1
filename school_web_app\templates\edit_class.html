{% extends 'base.html' %}

{% block title %}تعديل بيانات الفصل - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">تعديل بيانات الفصل</h5>
    </div>
    <div class="card-body">
        <form action="{{ url_for('edit_class', class_id=class_obj.id) }}" method="POST">
            <div class="mb-3">
                <label for="name" class="form-label">اسم الفصل</label>
                <input type="text" class="form-control" id="name" name="name" value="{{ class_obj.name }}" required>
            </div>
            <div class="mb-3">
                <label for="grade_level" class="form-label">المستوى</label>
                <select class="form-select" id="grade_level" name="grade_level" required>
                    <option value="">اختر المستوى...</option>
                    {% for i in range(1, 13) %}
                    <option value="{{ i }}" {% if class_obj.grade_level == i %}selected{% endif %}>{{ i }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="mb-3">
                <label for="teacher_id" class="form-label">المعلم المسؤول</label>
                <select class="form-select" id="teacher_id" name="teacher_id">
                    <option value="">اختر المعلم...</option>
                    {% for teacher in teachers %}
                    <option value="{{ teacher.id }}" {% if class_obj.teacher_id == teacher.id %}selected{% endif %}>{{ teacher.first_name }} {{ teacher.last_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('classes') }}" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-info">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
