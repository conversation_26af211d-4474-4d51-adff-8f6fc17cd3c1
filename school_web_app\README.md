# نظام إدارة المدرسة

نظام إدارة مدرسة متكامل مبني على Flask مع واجهة مستخدم متجاوبة باستخدام Bootstrap.

## الميزات

- إدارة الطلاب (إضافة، تعديل، حذف، عرض)
- إدارة المعلمين (إضافة، تعديل، حذف، عرض)
- إدارة الفصول (إضافة، تعديل، حذف، عرض)
- تسجيل الحضور وإنشاء تقارير
- إدارة الدرجات وإنشاء تقارير
- نظام تسجيل دخول متعدد المستويات (مدير، معلم، طالب)
- واجهة مستخدم متجاوبة تدعم اللغة العربية
- تصميم متوافق مع الأجهزة المحمولة

## متطلبات النظام

- Python 3.8 أو أحدث
- Flask
- Flask-SQLAlchemy
- SQLite (مدمج مع Python)

## التثبيت

1. قم بتنزيل أو استنساخ المشروع:

```bash
git clone https://github.com/yourusername/school-management-system.git
cd school-management-system
```

2. قم بإنشاء بيئة افتراضية وتفعيلها:

```bash
# على نظام Windows
python -m venv venv
venv\Scripts\activate

# على نظام Linux/Mac
python3 -m venv venv
source venv/bin/activate
```

3. قم بتثبيت المكتبات المطلوبة:

```bash
pip install -r requirements.txt
```

4. قم بتشغيل التطبيق:

```bash
flask run
```

5. افتح المتصفح وانتقل إلى `http://127.0.0.1:5000`

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## هيكل المشروع

```
school_web_app/
├── app.py                  # ملف التطبيق الرئيسي
├── routes.py               # مسارات التطبيق
├── requirements.txt        # متطلبات المشروع
├── static/                 # الملفات الثابتة
│   ├── css/                # ملفات CSS
│   ├── js/                 # ملفات JavaScript
│   └── img/                # الصور
└── templates/              # قوالب HTML
    ├── base.html           # القالب الأساسي
    ├── dashboard.html      # لوحة التحكم
    ├── login.html          # صفحة تسجيل الدخول
    ├── students.html       # إدارة الطلاب
    ├── teachers.html       # إدارة المعلمين
    ├── classes.html        # إدارة الفصول
    ├── attendance.html     # إدارة الحضور
    └── grades.html         # إدارة الدرجات
```

## الاستخدام

1. قم بتسجيل الدخول باستخدام بيانات الاعتماد الافتراضية.
2. استخدم لوحة التحكم للوصول إلى مختلف وظائف النظام.
3. يمكنك إضافة وتعديل وحذف الطلاب والمعلمين والفصول.
4. يمكنك تسجيل الحضور وإدارة الدرجات.
5. يمكنك إنشاء تقارير للحضور والدرجات.

## التخصيص

- يمكنك تعديل ملفات CSS في مجلد `static/css` لتغيير مظهر التطبيق.
- يمكنك تعديل قوالب HTML في مجلد `templates` لتغيير هيكل الصفحات.
- يمكنك إضافة المزيد من الوظائف عن طريق تعديل ملفات `app.py` و `routes.py`.

## الترخيص

هذا المشروع مرخص بموجب رخصة MIT. راجع ملف LICENSE للحصول على التفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى إرسال طلب سحب أو فتح مشكلة لاقتراح التغييرات أو الإضافات.
