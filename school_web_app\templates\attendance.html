{% extends 'base.html' %}

{% block title %}إدارة الحضور - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
        <h5 class="mb-0">إدارة الحضور</h5>
        <div>
            <a href="{{ url_for('take_attendance') }}" class="btn btn-dark">
                <i class="fas fa-clipboard-check"></i> تسجيل الحضور
            </a>
            <button type="button" class="btn btn-dark" data-bs-toggle="modal" data-bs-target="#reportModal">
                <i class="fas fa-file-alt"></i> تقرير الحضور
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">الفصل</span>
                    <select id="classFilter" class="form-select">
                        <option value="">جميع الفصول</option>
                        {% for class in classes %}
                        <option value="{{ class.id }}">{{ class.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">الحالة</span>
                    <select id="statusFilter" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="حاضر">حاضر</option>
                        <option value="غائب">غائب</option>
                        <option value="متأخر">متأخر</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">التاريخ</span>
                    <input type="date" id="dateFilter" class="form-control" value="{{ today }}">
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الطالب</th>
                        <th>الفصل</th>
                        <th>التاريخ</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attendance in attendances %}
                    <tr>
                        <td>{{ attendance.id }}</td>
                        <td>{{ attendance.student.first_name }} {{ attendance.student.last_name }}</td>
                        <td>{{ attendance.student.class.name if attendance.student.class else 'غير محدد' }}</td>
                        <td>{{ attendance.date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <span class="badge {% if attendance.status == 'حاضر' %}bg-success{% elif attendance.status == 'غائب' %}bg-danger{% else %}bg-warning{% endif %}">
                                {{ attendance.status }}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-info edit-attendance" data-id="{{ attendance.id }}" data-student="{{ attendance.student.first_name }} {{ attendance.student.last_name }}" data-status="{{ attendance.status }}" data-bs-toggle="modal" data-bs-target="#editAttendanceModal">
                                <i class="fas fa-edit"></i>
                            </button>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="6" class="text-center">لا توجد سجلات حضور لهذا اليوم</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Edit Attendance Modal -->
<div class="modal fade" id="editAttendanceModal" tabindex="-1" aria-labelledby="editAttendanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="editAttendanceModalLabel">تعديل حالة الحضور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editAttendanceForm" action="{{ url_for('edit_attendance', attendance_id=0) }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">الطالب</label>
                        <input type="text" class="form-control" id="studentName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <div class="d-flex justify-content-between">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="status" id="statusPresent" value="حاضر">
                                <label class="form-check-label" for="statusPresent">
                                    حاضر
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="status" id="statusAbsent" value="غائب">
                                <label class="form-check-label" for="statusAbsent">
                                    غائب
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="status" id="statusLate" value="متأخر">
                                <label class="form-check-label" for="statusLate">
                                    متأخر
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Attendance Report Modal -->
<div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-dark text-white">
                <h5 class="modal-title" id="reportModalLabel">تقرير الحضور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('attendance_report') }}" method="GET" target="_blank">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="report_class" class="form-label">الفصل</label>
                        <select class="form-select" id="report_class" name="class_id">
                            <option value="">جميع الفصول</option>
                            {% for class in classes %}
                            <option value="{{ class.id }}">{{ class.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="report_student" class="form-label">الطالب (اختياري)</label>
                        <select class="form-select" id="report_student" name="student_id">
                            <option value="">جميع الطلاب</option>
                            {% for student in students %}
                            <option value="{{ student.id }}">{{ student.first_name }} {{ student.last_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="report_start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="report_start_date" name="start_date" required>
                        </div>
                        <div class="col-md-6">
                            <label for="report_end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="report_end_date" name="end_date" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-dark">عرض التقرير</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Class filter
    document.getElementById('classFilter').addEventListener('change', function() {
        filterAttendance();
    });
    
    // Status filter
    document.getElementById('statusFilter').addEventListener('change', function() {
        filterAttendance();
    });
    
    // Date filter
    document.getElementById('dateFilter').addEventListener('change', function() {
        // In a real application, this would reload the data for the selected date via AJAX
        alert('في تطبيق حقيقي، سيتم تحميل بيانات الحضور للتاريخ المحدد');
    });
    
    // Filter attendance based on class and status
    function filterAttendance() {
        const classId = document.getElementById('classFilter').value;
        const status = document.getElementById('statusFilter').value;
        const tableRows = document.querySelectorAll('tbody tr');
        
        tableRows.forEach(row => {
            let display = true;
            
            if (classId && !row.cells[2].textContent.includes(classId)) {
                display = false;
            }
            
            if (status && !row.cells[4].textContent.includes(status)) {
                display = false;
            }
            
            row.style.display = display ? '' : 'none';
        });
    }
    
    // Edit attendance
    document.querySelectorAll('.edit-attendance').forEach(button => {
        button.addEventListener('click', function() {
            const attendanceId = this.getAttribute('data-id');
            const studentName = this.getAttribute('data-student');
            const status = this.getAttribute('data-status');
            
            document.getElementById('studentName').value = studentName;
            
            // Set the correct radio button
            if (status === 'حاضر') {
                document.getElementById('statusPresent').checked = true;
            } else if (status === 'غائب') {
                document.getElementById('statusAbsent').checked = true;
            } else if (status === 'متأخر') {
                document.getElementById('statusLate').checked = true;
            }
            
            // Update form action
            const form = document.getElementById('editAttendanceForm');
            form.action = form.action.replace('/0', `/${attendanceId}`);
        });
    });
    
    // Set default dates for report
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    document.getElementById('report_start_date').valueAsDate = firstDayOfMonth;
    document.getElementById('report_end_date').valueAsDate = today;
</script>
{% endblock %}
