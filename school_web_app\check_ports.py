#!/usr/bin/env python3
"""Check which ports are available"""
import socket

def check_port(host, port):
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result != 0
    except:
        return False

print("Checking available ports...")
print("-" * 40)

hosts = ['127.0.0.1', 'localhost']
ports = [3000, 5000, 8000, 8080, 9000]

for host in hosts:
    print(f"\nHost: {host}")
    for port in ports:
        status = "✅ Available" if check_port(host, port) else "❌ In use"
        print(f"  Port {port}: {status}")

print("\n" + "=" * 40)
print("Try running the server on any available port!")
print("Example: python -c \"from app import app; app.run(host='127.0.0.1', port=8000)\"")
