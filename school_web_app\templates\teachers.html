{% extends 'base.html' %}

{% block title %}إدارة المعلمين - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">إدارة المعلمين</h5>
        <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
            <i class="fas fa-plus"></i> إضافة معلم جديد
        </button>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" id="searchInput" class="form-control" placeholder="البحث عن معلم...">
                </div>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">المادة</span>
                    <select id="subjectFilter" class="form-select">
                        <option value="">جميع المواد</option>
                        {% for subject in subjects %}
                        <option value="{{ subject }}">{{ subject }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم</th>
                        <th>تاريخ الميلاد</th>
                        <th>الجنس</th>
                        <th>المادة</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>تاريخ التعيين</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for teacher in teachers.items %}
                    <tr>
                        <td>{{ teacher.id }}</td>
                        <td>{{ teacher.first_name }} {{ teacher.last_name }}</td>
                        <td>{{ teacher.date_of_birth.strftime('%Y-%m-%d') }}</td>
                        <td>{{ teacher.gender }}</td>
                        <td>{{ teacher.subject }}</td>
                        <td>{{ teacher.phone }}</td>
                        <td>{{ teacher.email }}</td>
                        <td>{{ teacher.hire_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <a href="{{ url_for('edit_teacher', teacher_id=teacher.id) }}" class="btn btn-sm btn-info">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button class="btn btn-sm btn-danger delete-teacher" data-id="{{ teacher.id }}" data-bs-toggle="modal" data-bs-target="#deleteTeacherModal">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="9" class="text-center">لا يوجد معلمين مسجلين</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if teachers.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if teachers.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('teachers', page=teachers.prev_num) }}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in teachers.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                    {% if page_num %}
                        {% if page_num == teachers.page %}
                        <li class="page-item active">
                            <a class="page-link" href="{{ url_for('teachers', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('teachers', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if teachers.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('teachers', page=teachers.next_num) }}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>

<!-- Add Teacher Modal -->
<div class="modal fade" id="addTeacherModal" tabindex="-1" aria-labelledby="addTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="addTeacherModalLabel">إضافة معلم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('add_teacher') }}" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">الاسم الأول</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">اسم العائلة</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="gender" class="form-label">الجنس</label>
                            <select class="form-select" id="gender" name="gender" required>
                                <option value="">اختر...</option>
                                <option value="ذكر">ذكر</option>
                                <option value="أنثى">أنثى</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="hire_date" class="form-label">تاريخ التعيين</label>
                            <input type="date" class="form-control" id="hire_date" name="hire_date" value="{{ now().strftime('%Y-%m-%d') }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="subject" class="form-label">المادة</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteTeacherModal" tabindex="-1" aria-labelledby="deleteTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteTeacherModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذا المعلم؟ هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteTeacherForm" action="{{ url_for('delete_teacher', teacher_id=0) }}" method="POST">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Search functionality
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchValue = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchValue) ? '' : 'none';
        });
    });
    
    // Subject filter
    document.getElementById('subjectFilter').addEventListener('change', function() {
        const subject = this.value;
        const tableRows = document.querySelectorAll('tbody tr');
        
        tableRows.forEach(row => {
            if (!subject) {
                row.style.display = '';
                return;
            }
            
            const subjectCell = row.cells[4].textContent;
            const matchesSubject = subjectCell === subject;
            row.style.display = matchesSubject ? '' : 'none';
        });
    });
    
    // Delete teacher
    document.querySelectorAll('.delete-teacher').forEach(button => {
        button.addEventListener('click', function() {
            const teacherId = this.getAttribute('data-id');
            const form = document.getElementById('deleteTeacherForm');
            form.action = form.action.replace('/0', `/${teacherId}`);
        });
    });
</script>
{% endblock %}
