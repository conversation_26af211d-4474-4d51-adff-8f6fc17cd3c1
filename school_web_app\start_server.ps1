# PowerShell script to start the school management system
Write-Host "================================" -ForegroundColor Green
Write-Host "   School Management System" -ForegroundColor Green
Write-Host "   نظام إدارة المدرسة" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""

# Change to script directory
Set-Location $PSScriptRoot

Write-Host "Installing requirements..." -ForegroundColor Yellow
pip install -r requirements.txt

Write-Host ""
Write-Host "Starting server..." -ForegroundColor Yellow
Write-Host ""

python run_server.py

Read-Host "Press Enter to exit"
