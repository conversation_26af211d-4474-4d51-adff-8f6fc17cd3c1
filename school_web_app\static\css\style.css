/* Custom CSS for School Management System */

/* Arabic Font */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

body {
    font-family: '<PERSON><PERSON>wal', sans-serif;
    background-color: #f8f9fa;
}

/* Navbar */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
}

/* Cards */
.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    font-weight: 700;
    border-bottom: 0;
}

/* Dashboard Cards */
.card .fa-3x {
    opacity: 0.8;
}

/* Tables */
.table th {
    font-weight: 700;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Forms */
.form-label {
    font-weight: 500;
}

.form-control, .form-select {
    border-radius: 8px;
}

.form-control:focus, .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 5px;
}

/* Footer */
footer {
    margin-top: 50px;
    font-weight: 500;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-title {
        font-size: 1.1rem;
    }
    
    .table {
        font-size: 0.9rem;
    }
    
    .btn {
        font-size: 0.9rem;
    }
}

/* Login Page */
.login-page {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
}

/* Custom Colors for Status */
.status-present {
    color: #28a745;
}

.status-absent {
    color: #dc3545;
}

.status-late {
    color: #ffc107;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Animations */
.card, .btn {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* RTL Specific Adjustments */
.dropdown-menu-end {
    right: 0;
    left: auto;
}

.modal-header .btn-close {
    margin: -0.5rem auto -0.5rem -0.5rem;
}

/* Custom Checkboxes and Radio Buttons */
.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Pagination */
.page-link {
    color: #0d6efd;
    border-radius: 5px;
    margin: 0 2px;
}

.page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Profile Page */
.profile-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
