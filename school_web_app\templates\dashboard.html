{% extends 'base.html' %}

{% block title %}لوحة التحكم - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body">
                <h2 class="card-title">مرحباً، {{ session.get('username') }}!</h2>
                <p class="card-text">مرحباً بك في نظام إدارة المدرسة. استخدم القائمة للوصول إلى الميزات المختلفة.</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-primary shadow h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-4">
                        <i class="fas fa-user-graduate fa-3x"></i>
                    </div>
                    <div class="col-8 text-end">
                        <h5 class="card-title">الطلاب</h5>
                        <h2 class="mb-0">{{ student_count }}</h2>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('students') }}" class="text-white text-decoration-none">عرض التفاصيل <i class="fas fa-arrow-circle-left"></i></a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-success shadow h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-4">
                        <i class="fas fa-chalkboard-teacher fa-3x"></i>
                    </div>
                    <div class="col-8 text-end">
                        <h5 class="card-title">المعلمون</h5>
                        <h2 class="mb-0">{{ teacher_count }}</h2>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('teachers') }}" class="text-white text-decoration-none">عرض التفاصيل <i class="fas fa-arrow-circle-left"></i></a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-info shadow h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-4">
                        <i class="fas fa-school fa-3x"></i>
                    </div>
                    <div class="col-8 text-end">
                        <h5 class="card-title">الفصول</h5>
                        <h2 class="mb-0">{{ class_count }}</h2>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('classes') }}" class="text-white text-decoration-none">عرض التفاصيل <i class="fas fa-arrow-circle-left"></i></a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-warning shadow h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-4">
                        <i class="fas fa-clipboard-check fa-3x"></i>
                    </div>
                    <div class="col-8 text-end">
                        <h5 class="card-title">الحضور اليوم</h5>
                        <h2 class="mb-0">{{ attendance_percentage }}%</h2>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('attendance') }}" class="text-white text-decoration-none">عرض التفاصيل <i class="fas fa-arrow-circle-left"></i></a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">آخر الطلاب المسجلين</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الفصل</th>
                                <th>تاريخ التسجيل</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in recent_students %}
                            <tr>
                                <td>{{ student.first_name }} {{ student.last_name }}</td>
                                <td>{{ student.class.name if student.class else 'غير محدد' }}</td>
                                <td>{{ student.enrollment_date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center">لا يوجد طلاب مسجلين حديثاً</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">أحدث الدرجات</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الطالب</th>
                                <th>المادة</th>
                                <th>الدرجة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for grade in recent_grades %}
                            <tr>
                                <td>{{ grade.student.first_name }} {{ grade.student.last_name }}</td>
                                <td>{{ grade.subject }}</td>
                                <td>{{ grade.score }}</td>
                                <td>{{ grade.date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center">لا توجد درجات مسجلة حديثاً</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
