{% extends 'base.html' %}

{% block title %}طلاب الفصل - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">طلاب الفصل: {{ class_obj.name }}</h5>
        <a href="{{ url_for('classes') }}" class="btn btn-light">
            <i class="fas fa-arrow-right"></i> العودة إلى الفصول
        </a>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" id="searchInput" class="form-control" placeholder="البحث عن طالب...">
                </div>
            </div>
            <div class="col-md-6 text-end">
                <p class="mb-0">
                    <strong>المستوى:</strong> {{ class_obj.grade_level }}
                    <br>
                    <strong>المعلم المسؤول:</strong> 
                    {% if class_obj.teacher_id %}
                        {% for teacher in teachers %}
                            {% if teacher.id == class_obj.teacher_id %}
                                {{ teacher.first_name }} {{ teacher.last_name }}
                            {% endif %}
                        {% endfor %}
                    {% else %}
                        غير محدد
                    {% endif %}
                </p>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم</th>
                        <th>تاريخ الميلاد</th>
                        <th>الجنس</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for student in students %}
                    <tr>
                        <td>{{ student.id }}</td>
                        <td>{{ student.first_name }} {{ student.last_name }}</td>
                        <td>{{ student.date_of_birth.strftime('%Y-%m-%d') }}</td>
                        <td>{{ student.gender }}</td>
                        <td>{{ student.phone }}</td>
                        <td>{{ student.email }}</td>
                        <td>
                            <a href="{{ url_for('edit_student', student_id=student.id) }}" class="btn btn-sm btn-info">
                                <i class="fas fa-edit"></i>
                            </a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">لا يوجد طلاب في هذا الفصل</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Search functionality
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchValue = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchValue) ? '' : 'none';
        });
    });
</script>
{% endblock %}
