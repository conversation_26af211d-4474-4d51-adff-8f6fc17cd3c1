#!/usr/bin/env python3
"""
Alternative server runner with different port options
"""
import sys
import socket
from app import app

def check_port(host, port):
    """Check if a port is available"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result != 0  # True if port is available
    except:
        return False

def find_available_port(host='127.0.0.1', start_port=5000):
    """Find an available port starting from start_port"""
    for port in range(start_port, start_port + 10):
        if check_port(host, port):
            return port
    return None

def main():
    print("=== نظام إدارة المدرسة ===")
    print("School Management System")
    print("-" * 40)
    
    # Try different host configurations
    configs = [
        ('127.0.0.1', 5000),
        ('localhost', 5000),
        ('127.0.0.1', 8000),
        ('127.0.0.1', 3000),
        ('0.0.0.0', 5000),
    ]
    
    for host, port in configs:
        if check_port(host, port):
            print(f"✅ Starting server on {host}:{port}")
            print(f"📱 Open your browser and go to: http://{host}:{port}")
            print(f"🔑 Login credentials:")
            print(f"   Username: admin")
            print(f"   Password: admin123")
            print("-" * 40)
            print("Press Ctrl+C to stop the server")
            
            try:
                app.run(debug=True, host=host, port=port, use_reloader=False)
                break
            except Exception as e:
                print(f"❌ Failed to start on {host}:{port}: {e}")
                continue
    else:
        print("❌ Could not find an available port. Please check your network settings.")
        
        # Try to find any available port
        available_port = find_available_port()
        if available_port:
            print(f"🔄 Trying alternative port: {available_port}")
            try:
                app.run(debug=True, host='127.0.0.1', port=available_port, use_reloader=False)
            except Exception as e:
                print(f"❌ Failed: {e}")

if __name__ == '__main__':
    main()
