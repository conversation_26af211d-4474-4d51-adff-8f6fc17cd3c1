{% extends 'base.html' %}

{% block title %}تسجيل الحضور - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تسجيل الحضور - {{ class_obj.name }}</h5>
        <a href="{{ url_for('attendance') }}" class="btn btn-dark">
            <i class="fas fa-arrow-right"></i> العودة إلى الحضور
        </a>
    </div>
    <div class="card-body">
        <form action="{{ url_for('take_attendance') }}" method="POST">
            <input type="hidden" name="class_id" value="{{ class_obj.id }}">
            
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">التاريخ</span>
                        <input type="date" name="date" class="form-control" value="{{ date }}" required>
                    </div>
                </div>
                <div class="col-md-8 text-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-success" id="markAllPresent">تحديد الكل حاضر</button>
                        <button type="button" class="btn btn-danger" id="markAllAbsent">تحديد الكل غائب</button>
                    </div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الطالب</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for student in students %}
                        <tr>
                            <td>{{ student.id }}</td>
                            <td>{{ student.first_name }} {{ student.last_name }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="status_{{ student.id }}" id="present_{{ student.id }}" value="حاضر" {% if attendances.get(student.id) == 'حاضر' or not attendances.get(student.id) %}checked{% endif %}>
                                    <label class="btn btn-outline-success" for="present_{{ student.id }}">حاضر</label>
                                    
                                    <input type="radio" class="btn-check" name="status_{{ student.id }}" id="absent_{{ student.id }}" value="غائب" {% if attendances.get(student.id) == 'غائب' %}checked{% endif %}>
                                    <label class="btn btn-outline-danger" for="absent_{{ student.id }}">غائب</label>
                                    
                                    <input type="radio" class="btn-check" name="status_{{ student.id }}" id="late_{{ student.id }}" value="متأخر" {% if attendances.get(student.id) == 'متأخر' %}checked{% endif %}>
                                    <label class="btn btn-outline-warning" for="late_{{ student.id }}">متأخر</label>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="3" class="text-center">لا يوجد طلاب في هذا الفصل</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-between mt-3">
                <a href="{{ url_for('attendance') }}" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ الحضور</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Mark all students as present
    document.getElementById('markAllPresent').addEventListener('click', function() {
        document.querySelectorAll('[id^="present_"]').forEach(radio => {
            radio.checked = true;
        });
    });
    
    // Mark all students as absent
    document.getElementById('markAllAbsent').addEventListener('click', function() {
        document.querySelectorAll('[id^="absent_"]').forEach(radio => {
            radio.checked = true;
        });
    });
</script>
{% endblock %}
