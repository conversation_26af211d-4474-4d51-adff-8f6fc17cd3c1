from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import os
from datetime import datetime, date
from functools import wraps

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///school.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Add custom functions to Jinja2 environment
app.jinja_env.globals.update(now=datetime.now)

# Initialize database
db = SQLAlchemy(app)

# Database Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)
    role = db.Column(db.String(20), nullable=False)  # admin, teacher, student

    def __repr__(self):
        return f'<User {self.username}>'

class Student(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    date_of_birth = db.Column(db.Date, nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    address = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    enrollment_date = db.Column(db.Date, default=datetime.utcnow)
    class_id = db.Column(db.Integer, db.ForeignKey('class.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    def __repr__(self):
        return f'<Student {self.first_name} {self.last_name}>'

class Teacher(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    date_of_birth = db.Column(db.Date, nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    address = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    hire_date = db.Column(db.Date, default=datetime.utcnow)
    subject = db.Column(db.String(50))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    def __repr__(self):
        return f'<Teacher {self.first_name} {self.last_name}>'

class Class(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    grade_level = db.Column(db.Integer, nullable=False)
    teacher_id = db.Column(db.Integer, db.ForeignKey('teacher.id'))
    students = db.relationship('Student', backref='class', lazy=True)

    def __repr__(self):
        return f'<Class {self.name}>'

class Attendance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('student.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), nullable=False)  # present, absent, late
    student = db.relationship('Student', backref='attendances')

    def __repr__(self):
        return f'<Attendance {self.student_id} {self.date}>'

class Grade(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('student.id'), nullable=False)
    subject = db.Column(db.String(50), nullable=False)
    score = db.Column(db.Float, nullable=False)
    date = db.Column(db.Date, default=datetime.utcnow)
    student = db.relationship('Student', backref='grades')

    def __repr__(self):
        return f'<Grade {self.student_id} {self.subject}>'

# Login required decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('يرجى تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# Admin required decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'role' not in session or session['role'] != 'admin':
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

# Teacher or admin required decorator
def teacher_or_admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'role' not in session or session['role'] not in ['admin', 'teacher']:
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

# Routes
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # Get counts for dashboard
    student_count = Student.query.count()
    teacher_count = Teacher.query.count()
    class_count = Class.query.count()

    # Calculate attendance percentage for today
    today = date.today()
    total_students = Student.query.count()
    present_students = Attendance.query.filter_by(date=today, status='حاضر').count()
    attendance_percentage = int((present_students / total_students) * 100) if total_students > 0 else 0

    # Get recent students
    recent_students = Student.query.order_by(Student.enrollment_date.desc()).limit(5).all()

    # Get recent grades
    recent_grades = Grade.query.order_by(Grade.date.desc()).limit(5).all()

    return render_template('dashboard.html',
                          student_count=student_count,
                          teacher_count=teacher_count,
                          class_count=class_count,
                          attendance_percentage=attendance_percentage,
                          recent_students=recent_students,
                          recent_grades=recent_grades)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if 'user_id' in session:
        return redirect(url_for('index'))

    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password, password):
            session['user_id'] = user.id
            session['username'] = user.username
            session['role'] = user.role
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')

    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/attendance/edit/<int:attendance_id>', methods=['GET', 'POST'])
@login_required
def edit_attendance(attendance_id):
    attendance = Attendance.query.get_or_404(attendance_id)
    if request.method == 'POST':
        new_status = request.form.get('status')
        new_date = request.form.get('date')
        if new_status:
            attendance.status = new_status
        if new_date:
            try:
                attendance.date = datetime.strptime(new_date, '%Y-%m-%d').date()
            except ValueError:
                flash('صيغة التاريخ غير صحيحة', 'danger')
                return render_template('edit_attendance.html', attendance=attendance)
        db.session.commit()
        flash('تم تحديث الحضور بنجاح', 'success')
        return redirect(url_for('index'))
    return render_template('edit_attendance.html', attendance=attendance)

@app.route('/grade/edit/<int:grade_id>', methods=['GET', 'POST'])
@login_required
def edit_grade(grade_id):
    grade = Grade.query.get_or_404(grade_id)
    if request.method == 'POST':
        new_subject = request.form.get('subject')
        new_score = request.form.get('score')
        new_date = request.form.get('date')
        if new_subject:
            grade.subject = new_subject
        if new_score:
            try:
                grade.score = float(new_score)
            except ValueError:
                flash('صيغة الدرجة غير صحيحة', 'danger')
                return render_template('edit_grade.html', grade=grade)
        if new_date:
            try:
                grade.date = datetime.strptime(new_date, '%Y-%m-%d').date()
            except ValueError:
                flash('صيغة التاريخ غير صحيحة', 'danger')
                return render_template('edit_grade.html', grade=grade)
        db.session.commit()
        flash('تم تحديث الدرجة بنجاح', 'success')
        return redirect(url_for('index'))
    return render_template('edit_grade.html', grade=grade)

# Initialize the database
with app.app_context():
    db.create_all()

    # Create admin user if not exists
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            password=generate_password_hash('admin123'),
            role='admin'
        )
        db.session.add(admin)
        db.session.commit()

# Import and register routes
try:
    from routes import register_routes
    register_routes(app, db, login_required, admin_required, teacher_or_admin_required,
                   User, Student, Teacher, Class, Attendance, Grade)
    print("Routes registered successfully")
except ImportError as e:
    print(f"Warning: routes.py not found or has errors: {e}")
    print("Only basic functionality will be available.")

if __name__ == '__main__':
    print("Starting Flask application on http://localhost:5000")
    print("Press Ctrl+C to stop the server")
    app.run(debug=True, host='0.0.0.0', port=5000)
