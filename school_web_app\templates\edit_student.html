{% extends 'base.html' %}

{% block title %}تعديل بيانات الطالب - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">تعديل بيانات الطالب</h5>
    </div>
    <div class="card-body">
        <form action="{{ url_for('edit_student', student_id=student.id) }}" method="POST">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="first_name" class="form-label">الاسم الأول</label>
                    <input type="text" class="form-control" id="first_name" name="first_name" value="{{ student.first_name }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="last_name" class="form-label">اسم العائلة</label>
                    <input type="text" class="form-control" id="last_name" name="last_name" value="{{ student.last_name }}" required>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                    <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" value="{{ student.date_of_birth.strftime('%Y-%m-%d') }}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="gender" class="form-label">الجنس</label>
                    <select class="form-select" id="gender" name="gender" required>
                        <option value="">اختر...</option>
                        <option value="ذكر" {% if student.gender == 'ذكر' %}selected{% endif %}>ذكر</option>
                        <option value="أنثى" {% if student.gender == 'أنثى' %}selected{% endif %}>أنثى</option>
                    </select>
                </div>
            </div>
            <div class="mb-3">
                <label for="address" class="form-label">العنوان</label>
                <textarea class="form-control" id="address" name="address" rows="2">{{ student.address }}</textarea>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">رقم الهاتف</label>
                    <input type="tel" class="form-control" id="phone" name="phone" value="{{ student.phone }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="email" name="email" value="{{ student.email }}">
                </div>
            </div>
            <div class="mb-3">
                <label for="class_id" class="form-label">الفصل</label>
                <select class="form-select" id="class_id" name="class_id">
                    <option value="">اختر الفصل...</option>
                    {% for class in classes %}
                    <option value="{{ class.id }}" {% if student.class_id == class.id %}selected{% endif %}>{{ class.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('students') }}" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
