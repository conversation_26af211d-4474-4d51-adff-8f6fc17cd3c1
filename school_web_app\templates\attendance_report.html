{% extends 'base.html' %}

{% block title %}تقرير الحضور - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقرير الحضور</h5>
        <button class="btn btn-light" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة التقرير
        </button>
    </div>
    <div class="card-body">
        <!-- Filter Form -->
        <form method="GET" action="{{ url_for('attendance_report') }}" class="mb-4">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">الفصل</label>
                    <select name="class_id" class="form-select">
                        <option value="">جميع الفصول</option>
                        {% for class in classes %}
                        <option value="{{ class.id }}" {% if request.args.get('class_id')|string == class.id|string %}selected{% endif %}>
                            {{ class.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الطالب</label>
                    <select name="student_id" class="form-select">
                        <option value="">جميع الطلاب</option>
                        {% for student in students %}
                        <option value="{{ student.id }}" {% if request.args.get('student_id')|string == student.id|string %}selected{% endif %}>
                            {{ student.first_name }} {{ student.last_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="start_date" class="form-control" value="{{ request.args.get('start_date', '') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="end_date" class="form-control" value="{{ request.args.get('end_date', '') }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">تطبيق الفلتر</button>
                </div>
            </div>
        </form>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr class="table-dark">
                        <th>#</th>
                        <th>الطالب</th>
                        <th>الفصل</th>
                        <th>التاريخ</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attendance in attendances %}
                    <tr>
                        <td>{{ attendance.id }}</td>
                        <td>{{ attendance.student.first_name }} {{ attendance.student.last_name }}</td>
                        <td>{{ attendance.student.class.name if attendance.student.class else 'غير محدد' }}</td>
                        <td>{{ attendance.date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <span class="badge {% if attendance.status == 'حاضر' %}bg-success{% elif attendance.status == 'غائب' %}bg-danger{% else %}bg-warning{% endif %}">
                                {{ attendance.status }}
                            </span>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="5" class="text-center">لا توجد سجلات حضور للفترة المحددة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        {% if attendances %}
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">ملخص الحضور</h6>
                    </div>
                    <div class="card-body">
                        {% set present_count = attendances|selectattr('status', 'equalto', 'حاضر')|list|length %}
                        {% set absent_count = attendances|selectattr('status', 'equalto', 'غائب')|list|length %}
                        {% set late_count = attendances|selectattr('status', 'equalto', 'متأخر')|list|length %}
                        {% set total_count = attendances|length %}
                        
                        <div class="row">
                            <div class="col-6">
                                <p><strong>إجمالي السجلات:</strong></p>
                                <p><strong>حاضر:</strong></p>
                                <p><strong>غائب:</strong></p>
                                <p><strong>متأخر:</strong></p>
                                <p><strong>نسبة الحضور:</strong></p>
                            </div>
                            <div class="col-6 text-end">
                                <p>{{ total_count }}</p>
                                <p>{{ present_count }}</p>
                                <p>{{ absent_count }}</p>
                                <p>{{ late_count }}</p>
                                <p>{{ ((present_count + late_count) / total_count * 100)|round(2) if total_count > 0 else 0 }}%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">الطلاب الأكثر غياباً</h6>
                    </div>
                    <div class="card-body">
                        {% set students = {} %}
                        {% for attendance in attendances %}
                            {% if attendance.status == 'غائب' %}
                                {% if attendance.student.id in students %}
                                    {% set _ = students.update({attendance.student.id: students[attendance.student.id] + 1}) %}
                                {% else %}
                                    {% set _ = students.update({attendance.student.id: 1}) %}
                                {% endif %}
                            {% endif %}
                        {% endfor %}
                        
                        <ul class="list-group">
                            {% set top_absentees = [] %}
                            {% for student_id, count in students|dictsort(by='value', reverse=true)|slice(5) %}
                                {% set found = false %}
                                {% for attendance in attendances %}
                                    {% if attendance.student.id == student_id and not found %}
                                        {% set _ = top_absentees.append({
                                            'name': attendance.student.first_name ~ ' ' ~ attendance.student.last_name,
                                            'count': count
                                        }) %}
                                        {% set found = true %}
                                    {% endif %}
                                {% endfor %}
                            {% endfor %}
                            
                            {% if top_absentees %}
                                {% for student in top_absentees %}
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        {{ student.name }}
                                        <span class="badge bg-danger rounded-pill">{{ student.count }}</span>
                                    </li>
                                {% endfor %}
                            {% else %}
                                <li class="list-group-item">لا توجد بيانات غياب</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        .navbar, .footer, .btn, .no-print {
            display: none !important;
        }
        
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        
        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
            border-bottom: 1px solid #dee2e6 !important;
        }
        
        .badge {
            border: 1px solid #000 !important;
            color: #000 !important;
        }
        
        .bg-success {
            background-color: #ffffff !important;
            border: 1px solid #28a745 !important;
        }
        
        .bg-danger {
            background-color: #ffffff !important;
            border: 1px solid #dc3545 !important;
        }
        
        .bg-warning {
            background-color: #ffffff !important;
            border: 1px solid #ffc107 !important;
        }
        
        .table-dark th {
            background-color: #f8f9fa !important;
            color: #000 !important;
            border: 1px solid #dee2e6 !important;
        }
    }
</style>
{% endblock %}
