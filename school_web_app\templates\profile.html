{% extends 'base.html' %}

{% block title %}الملف الشخصي - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">الملف الشخصي</h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-circle fa-5x text-primary"></i>
                </div>
                <h4>{{ user.username }}</h4>
                <p class="text-muted">
                    {% if user.role == 'admin' %}
                        <span class="badge bg-danger">مدير النظام</span>
                    {% elif user.role == 'teacher' %}
                        <span class="badge bg-success">معلم</span>
                    {% elif user.role == 'student' %}
                        <span class="badge bg-info">طالب</span>
                    {% endif %}
                </p>
                
                {% if teacher %}
                <div class="text-start mt-3">
                    <p><strong>الاسم:</strong> {{ teacher.first_name }} {{ teacher.last_name }}</p>
                    <p><strong>المادة:</strong> {{ teacher.subject }}</p>
                    <p><strong>البريد الإلكتروني:</strong> {{ teacher.email }}</p>
                    <p><strong>رقم الهاتف:</strong> {{ teacher.phone }}</p>
                </div>
                {% endif %}
                
                {% if student %}
                <div class="text-start mt-3">
                    <p><strong>الاسم:</strong> {{ student.first_name }} {{ student.last_name }}</p>
                    <p><strong>الفصل:</strong> {{ student.class.name if student.class else 'غير محدد' }}</p>
                    <p><strong>البريد الإلكتروني:</strong> {{ student.email }}</p>
                    <p><strong>رقم الهاتف:</strong> {{ student.phone }}</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card shadow">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">تغيير كلمة المرور</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('change_password') }}" method="POST">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <button type="submit" class="btn btn-danger w-100">تغيير كلمة المرور</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">الإحصائيات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if user.role == 'admin' %}
                        <div class="col-md-4 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h1>{{ stats.student_count }}</h1>
                                    <p class="mb-0">عدد الطلاب</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h1>{{ stats.teacher_count }}</h1>
                                    <p class="mb-0">عدد المعلمين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h1>{{ stats.class_count }}</h1>
                                    <p class="mb-0">عدد الفصول</p>
                                </div>
                            </div>
                        </div>
                    {% elif user.role == 'teacher' %}
                        <div class="col-md-4 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h1>{{ stats.class_count }}</h1>
                                    <p class="mb-0">عدد الفصول</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h1>{{ stats.student_count }}</h1>
                                    <p class="mb-0">عدد الطلاب</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h1>{{ stats.avg_grade }}</h1>
                                    <p class="mb-0">متوسط الدرجات</p>
                                </div>
                            </div>
                        </div>
                    {% elif user.role == 'student' %}
                        <div class="col-md-4 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h1>{{ stats.subject_count }}</h1>
                                    <p class="mb-0">عدد المواد</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h1>{{ stats.avg_grade }}</h1>
                                    <p class="mb-0">متوسط الدرجات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h1>{{ stats.attendance_rate }}%</h1>
                                    <p class="mb-0">نسبة الحضور</p>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        {% if user.role == 'student' %}
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">درجاتي</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>الدرجة</th>
                                <th>التقدير</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for grade in student.grades %}
                            <tr>
                                <td>{{ grade.subject }}</td>
                                <td>{{ grade.score }}</td>
                                <td>
                                    {% if grade.score >= 90 %}
                                        <span class="badge bg-success">ممتاز</span>
                                    {% elif grade.score >= 80 %}
                                        <span class="badge bg-info">جيد جداً</span>
                                    {% elif grade.score >= 70 %}
                                        <span class="badge bg-primary">جيد</span>
                                    {% elif grade.score >= 60 %}
                                        <span class="badge bg-warning">مقبول</span>
                                    {% else %}
                                        <span class="badge bg-danger">ضعيف</span>
                                    {% endif %}
                                </td>
                                <td>{{ grade.date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center">لا توجد درجات مسجلة</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
